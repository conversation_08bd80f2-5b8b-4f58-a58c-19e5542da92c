CREATE SCHEMA "capehorn_app";
--> statement-breakpoint
CREATE TABLE "capehorn_app"."dashboard" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "capehorn_app"."dashboard_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"name" varchar(255) NOT NULL,
	"description" varchar(255),
	"thumbnail_config" jsonb NOT NULL,
	"dash_config" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"owner" varchar(255) NOT NULL
);
