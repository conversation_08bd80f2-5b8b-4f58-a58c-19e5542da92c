import { desc, eq } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardTable } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const dashboards = await db
      .select()
      .from(dashboardTable)
      .where(eq(dashboardTable.owner, resourceId))
      .orderBy(desc(dashboardTable.updatedAt));
    return dashboards;
  } catch (error) {
    console.error("Error fetching dashboards:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Failed to fetch dashboards",
    });
  }
});
