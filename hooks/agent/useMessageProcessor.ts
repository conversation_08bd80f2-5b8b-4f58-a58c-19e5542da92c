import type { Observable, Subscription } from "rxjs";
import {
  BehaviorSubject,
  Subject,
  scan,
  shareReplay,
  takeUntil,
  tap,
} from "rxjs";
import { computed, onUnmounted, readonly, ref } from "vue";
import type { StreamEvent } from "~/hooks/agent/types";
import type { Message, TextMessage, ToolCallMessage } from "~/types/message";

export interface ChatState {
  messages: Array<Message>;
  status: "idle" | "pending" | "processing" | "done" | "canceled";
}

export interface StreamProcessorOptions {
  initialState?: Partial<ChatState>;
}

export const useMessageStreamProcessor = (
  options: StreamProcessorOptions = {}
) => {
  const { initialState } = options;
  const destroy$ = new Subject<void>();
  const messages = ref<Array<Message>>([]);
  const status = ref<ChatState["status"]>("idle");
  let streamEvents$ = new Subject<StreamEvent<unknown>>();
  let processorState$ = new BehaviorSubject<ChatState>({
    messages: [],
    status: "idle",
    ...initialState,
  });
  let processorStateSubscription: Subscription;
  let processedState$: Observable<ChatState>;
  let processedStateSubscription: Subscription;

  const handleTextEvent = (
    state: ChatState,
    event: StreamEvent<unknown>
  ): ChatState => {
    if (event.type !== "text") {
      return state;
    }
    const text = event.payload.text || "";

    let currentAssistantMessage = state.messages[state.messages.length - 1];
    if (
      !currentAssistantMessage ||
      currentAssistantMessage.role !== "assistant"
    ) {
      currentAssistantMessage = {
        role: "assistant",
        content: [],
      };
      state.messages.push(currentAssistantMessage);
    }

    const lastContent =
      currentAssistantMessage.content[
        currentAssistantMessage.content.length - 1
      ];
    if (lastContent && lastContent.type === "text") {
      const updatedContent = { ...lastContent, text: lastContent.text + text };
      currentAssistantMessage.content[
        currentAssistantMessage.content.length - 1
      ] = updatedContent;
    } else {
      currentAssistantMessage.content.push({ type: "text", text });
    }

    return state;
  };

  const handleToolCallEvent = (
    state: ChatState,
    event: StreamEvent<unknown>
  ): ChatState => {
    if (event.type !== "tool-call") {
      return state;
    }

    const { toolCallId, toolName } = event.payload;

    if (!toolCallId || !toolName) {
      return state;
    }

    const args = (event.payload.args || {}) as Record<string, unknown>;

    // 获取或创建当前的助手消息
    let currentAssistantMessage = state.messages[state.messages.length - 1];
    if (
      !currentAssistantMessage ||
      currentAssistantMessage.role !== "assistant"
    ) {
      currentAssistantMessage = {
        role: "assistant",
        content: [],
      };
      state.messages.push(currentAssistantMessage);
    }

    // 在助手消息的content中添加或更新工具调用
    const lastContent =
      currentAssistantMessage.content[
        currentAssistantMessage.content.length - 1
      ];
    if (
      lastContent &&
      lastContent.type === "tool-call" &&
      lastContent.toolCallId === toolCallId
    ) {
      lastContent.args = args;
    } else {
      currentAssistantMessage.content.push({
        type: "tool-call",
        toolCallId,
        toolName,
        args: args || {},
      });
    }

    return state;
  };

  const handleToolResultEvent = (
    state: ChatState,
    event: StreamEvent<unknown>
  ): ChatState => {
    if (event.type !== "tool-result") {
      return state;
    }
    const { toolCallId, result } = event.payload;

    if (!toolCallId) {
      return state;
    }

    // 在所有消息的content中查找对应的工具调用并更新结果
    for (const message of state.messages) {
      if (message.role === "assistant") {
        for (const content of message.content) {
          if (
            content.type === "tool-call" &&
            content.toolCallId === toolCallId &&
            !content.result
          ) {
            content.result = result as Record<string, unknown> | undefined;
            return state;
          }
        }
      }
    }

    return state;
  };

  const handleUserMessageEvent = (
    state: ChatState,
    event: StreamEvent<unknown>
  ): ChatState => {
    if (event.type !== "user-message") {
      return state;
    }

    state.messages.push({
      role: "user",
      content: [{ type: "text", text: event.payload.message }],
    });

    return state;
  };

  const createStreamProcesssorPipeline = () => {
    processedStateSubscription?.unsubscribe();

    processedState$ = streamEvents$.pipe(
      scan((state: ChatState, event: StreamEvent<unknown>) => {
        const newState = { ...state };

        switch (event.type) {
          case "text":
            return handleTextEvent(newState, event);
          case "tool-call":
            return handleToolCallEvent(newState, event);
          case "tool-result":
            return handleToolResultEvent(newState, event);
          case "step-start":
            newState.status = "processing";
            break;
          case "step-finish":
            break;
          case "error":
            newState.status = "done";
            break;
          case "update-process-status":
            newState.status = event.payload.status;
            break;
          case "user-message":
            return handleUserMessageEvent(newState, event);
        }

        return newState;
      }, processorState$.value),
      tap((state) => {
        processorState$.next(state);
      }),
      shareReplay(1),
      takeUntil(destroy$)
    );
    processedStateSubscription = processedState$.subscribe();
  };

  const createProcessorStateSubscription = (newState?: Partial<ChatState>) => {
    processorStateSubscription?.unsubscribe();
    processorState$.complete();

    processorState$ = new BehaviorSubject<ChatState>({
      messages: [],
      status: "idle",
      ...newState,
    });

    processorStateSubscription = processorState$.subscribe((state) => {
      messages.value = state.messages.map((message) => {
        if (message.role === "user") {
          return {
            ...message,
            content: [...message.content] as Array<TextMessage>,
          };
        } else {
          return {
            ...message,
            content: [...message.content] as Array<
              TextMessage | ToolCallMessage
            >,
          };
        }
      });
      status.value = state.status;
    });
  };

  createProcessorStateSubscription(initialState);
  createStreamProcesssorPipeline();

  const isProcessing = computed(() => {
    return status.value === "processing";
  });

  const isPending = computed(() => {
    return status.value === "pending";
  });

  const isDone = computed(() => {
    return status.value === "done";
  });

  const isCanceled = computed(() => {
    return status.value === "canceled";
  });

  const emitEvent = (event: StreamEvent<unknown>) => {
    streamEvents$.next(event);
  };

  const setState = (state: Partial<ChatState>) => {
    const currentState = processorState$.value;
    processorState$.next({ ...currentState, ...state });
  };

  const reset = (
    newState?: Partial<ChatState>,
    { resetMessages = false }: { resetMessages?: boolean } = {}
  ) => {
    streamEvents$.complete();
    streamEvents$ = new Subject<StreamEvent<unknown>>();

    createProcessorStateSubscription();
    createStreamProcesssorPipeline();

    const currentState = processorState$.value;
    const nextState = {
      status: "idle" as const,
      ...newState,
      messages: resetMessages
        ? [...(newState?.messages ?? [])]
        : [...currentState.messages, ...(newState?.messages ?? [])],
    };
    processorState$.next(nextState);
  };

  const cleanup = () => {
    destroy$.next();
    destroy$.complete();
    streamEvents$.complete();
    processorStateSubscription?.unsubscribe();
    processedStateSubscription?.unsubscribe();
  };

  onUnmounted(() => {
    cleanup();
  });

  return {
    // state
    messages: readonly(messages),
    status: readonly(status),

    // status computed properties
    isProcessing,
    isPending,
    isDone,
    isCanceled,

    // stream object
    processorState$: processorState$.asObservable(),
    streamEvents$: streamEvents$.asObservable(),

    // actions
    emitEvent,
    setState,
    reset,
    cleanup,
    getStreamEvents: () => streamEvents$.asObservable(),
  };
};
