// import { processDataWithClue } from "./chartTypeHelper";
import cube from "@cubejs-client/core";
import type { CancellableRequest } from "../types";
import type { Query as DashboardQuery, ResultSet } from "@cubejs-client/core";

// Monitor 数据获取器类
class MonitorDataFetcher {
  private baseUrl: string;
  private cubeApi: ReturnType<typeof cube>;
  private activeControllers: Map<string, { abort: () => void }> = new Map();

  constructor(apiUrl: string = "/monitor-api") {
    this.baseUrl = apiUrl;
    this.cubeApi = cube("CUBE-API-TOKEN", {
      apiUrl: `${apiUrl}/cubejs-api/v1`,
    });
  }

  // 获取仪表板数据（可取消），支持自定义查询参数
  fetchDashboardData(id: string, query?: DashboardQuery): CancellableRequest {
    const requestKey = `dashboard-${id}`;
    this.cancelRequest(requestKey);
    let cancelled = false;
    const promise = this.performFetch(id, () => cancelled, query);
    const cancel = () => {
      cancelled = true;
      this.activeControllers.delete(requestKey);
    };
    this.activeControllers.set(requestKey, { abort: cancel });
    return { promise, cancel };
  }

  // 执行实际的数据获取，支持自定义查询
  private async performFetch(
    id: string,
    isCancelled: () => boolean,
    query?: DashboardQuery
  ): Promise<ResultSet> {
    try {
      const cubeQuery = {
        ...query,
      };
      const result = await this.cubeApi.load(cubeQuery);
      if (isCancelled()) throw new Error("请求已取消");
      return result;
    } catch (error) {
      if (isCancelled()) throw new Error("请求已取消");
      throw error;
    }
  }

  // 取消特定请求
  cancelRequest(requestKey: string) {
    const controller = this.activeControllers.get(requestKey);
    if (controller) {
      controller.abort();
      this.activeControllers.delete(requestKey);
    }
  }

  // 取消所有活跃请求
  cancelAllRequests() {
    this.activeControllers.forEach((controller) => controller.abort());
    this.activeControllers.clear();
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isInitialized: true,
      baseUrl: this.baseUrl,
      activeRequests: this.activeControllers.size,
    };
  }
}

// 创建全局 Monitor 数据获取器实例
export const monitorDataFetcher = new MonitorDataFetcher();

// 获取仪表板数据，支持自定义查询
export function getClueDashboardData(
  id: string,
  query?: DashboardQuery
): CancellableRequest {
  return monitorDataFetcher.fetchDashboardData(id, query);
}

// 取消请求
export function cancelClueRequest(id: string) {
  monitorDataFetcher.cancelRequest(`dashboard-${id}`);
}

// 取消所有请求
export function cancelAllClueRequests() {
  monitorDataFetcher.cancelAllRequests();
}
