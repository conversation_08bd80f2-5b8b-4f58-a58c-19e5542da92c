<script setup lang="ts">
import { ChevronDownIcon } from "lucide-vue-next";
import { ref, onMounted, onUnmounted } from "vue";

const { isRunning } = defineProps<{
  isRunning?: boolean;
}>();

const isVisible = ref(false);

const emit = defineEmits<{
  (e: "to-bottom"): void;
}>();

const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: "smooth",
  });
  emit("to-bottom");
};

const handleVisible = () => {
  const isNearBottom =
    window.innerHeight + window.scrollY >= document.body.offsetHeight - 100;
  isVisible.value = !isNearBottom;
};

let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  window.addEventListener("scroll", handleVisible);
  handleVisible();
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        handleVisible();
      });
    });
    resizeObserver.observe(document.body);
  }
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleVisible);
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<template>
  <div
    class="absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
    }"
    @click="scrollToBottom"
  >
    <!-- Button with animated border -->
    <div class="relative">
      <!-- Main circular button -->
      <div
        class="w-9 h-9 rounded-full bg-white flex items-center justify-center relative overflow-hidden"
        :class="{
          'shadow-md': !isRunning,
        }"
      >
        <!-- Full circle border (when not running) -->
        <div
          v-if="!isRunning || !isVisible"
          class="absolute inset-0 rounded-full border-2 border-gray-300"
        />

        <!-- Animated quarter circle border (when running) -->
        <div
          v-if="isRunning && isVisible"
          class="absolute inset-0"
        >
          <div class="quarter-circle-border animate-spin" />
        </div>

        <!-- Stationary downward arrow -->
        <ChevronDownIcon :size="16" class="text-gray-600" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.quarter-circle-border {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: #3a7ca5; /* Theme color for the animated segment */
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
