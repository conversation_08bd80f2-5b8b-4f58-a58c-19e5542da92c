<script setup lang="ts">
import { Toaster } from "@/components/ui/sonner";
import "vue-sonner/style.css"; // vue-sonner v2 requires this import
import { TooltipProvider } from "@/components/ui/tooltip";
</script>

<template>
  <Toaster
    :toast-options="{
      unstyled: true,
      classes: {
        error:
          'text-red-400 p-3 rounded-md-new text-sm border border-stone-200 shadow',
        success:
          'text-green-400 p-3 rounded-md-new text-sm border border-stone-200 shadow',
        warning:
          'text-yellow-400 p-3 rounded-md-new text-sm border border-stone-200 shadow',
        info: 'bg-blue-400 p-3 rounded-md-new text-sm border border-stone-200 shadow',
      },
    }"
  />
  <TooltipProvider>
    <div>
      <NuxtLayout>
        <NuxtPage />
      </NuxtLayout>
    </div>
  </TooltipProvider>
</template>
