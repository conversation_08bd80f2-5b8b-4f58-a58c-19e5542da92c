import { isEqual, sortBy } from "lodash-es";
import { defineStore } from "pinia";
import type { Subscription } from "rxjs";
import { convertMessagesToStreamEvents } from "~/hooks/agent/message";
import { useAgent } from "~/hooks/agent/useAgent";
import { MONITOR_AGENT } from "~/modules/monitor/const";
import { useMonitorDashboard } from "../hooks/useMonitorDashboard";
import { useStreamProcessor } from "../hooks/useStreamProcessor";
import { useThreadMessages } from "../hooks/useThreadMessages";
import type { Chart, DashboardLayout } from "../types";

export const useMonitorEditStore = defineStore("monitorEdit", () => {
  const { dashboard } = useMonitorDashboard();
  const streamProcessor = useStreamProcessor();
  const agent = useAgent(
    MONITOR_AGENT,
    () => streamProcessor,
    dashboard.value?.threadId
  );
  const messages = useThreadMessages();
  const isRunning = ref(false);
  const dashboardLayout = ref<DashboardLayout>([]);
  const dashboardCharts = ref<Record<string, Chart>>({});

  watch(
    dashboard,
    (newDashboard) => {
      if (newDashboard) {
        agent.setThreadId(newDashboard?.threadId ?? "");
        dashboardLayout.value = (newDashboard.dashConfig?.layout ?? []).map(
          (l) => ({
            ...l,
          })
        );
        const newCharts: Record<string, Chart> = {};
        for (const [id, config] of Object.entries(
          newDashboard.dashConfig?.charts ?? {}
        )) {
          newCharts[id] = { ...config };
        }
        dashboardCharts.value = newCharts;
        console.log(dashboardLayout.value, dashboardCharts.value);
      }
    },
    {
      immediate: true,
    }
  );

  const sendMessageToAgent = async (message: string) => {
    try {
      isRunning.value = true;
      await agent.start(message, {
        dashboardId: dashboard.value?.id,
        threadId: dashboard.value?.threadId,
        resourceId: dashboard.value?.owner,
      });
    } catch (error: unknown) {
      console.error(error);
    } finally {
      isRunning.value = false;
    }
  };

  const cancelAgentResponse = () => {
    isRunning.value = false;
    agent.cancel();
  };

  watch(messages, async (newMessages) => {
    if (newMessages && (newMessages?.length ?? 0) > 0) {
      streamProcessor.reset({}, { resetMessages: true });

      const events = convertMessagesToStreamEvents(newMessages, undefined);
      events.forEach((event) => {
        streamProcessor.emitEvent(event);
      });
    }
  });

  const $reset = () => {
    dashboardLayout.value = [];
    dashboardCharts.value = {};
    streamProcessor.reset({}, { resetMessages: true });
  };

  let subscription: Subscription | null = null;
  watch(isRunning, (newIsRunning) => {
    if (newIsRunning) {
      console.log("start subscription");
      subscription = streamProcessor.getStreamEvents().subscribe((event) => {
        if (event.type === "tool-call") {
          if (event.payload.toolName === "updateLayoutTool") {
            const { layout } = event.payload.args as {
              layout: DashboardLayout;
            };
            const lastCharts = sortBy(layout.map((item) => item.i));
            const currentCharts = sortBy(
              dashboardLayout.value.map((item) => item.i)
            );
            dashboardLayout.value = layout;
            if (!isEqual(lastCharts, currentCharts)) {
              dashboardCharts.value = {};
            }
          } else if (event.payload.toolName === "updateChartTool") {
            const chart = event.payload.args as Chart;
            dashboardCharts.value[chart.id] = chart;
          }
        }
      });
    } else {
      if (subscription) {
        console.log("unsubscribe");
        subscription.unsubscribe();
      }
    }
  });

  return {
    dashboard,
    messages: streamProcessor.messages,
    dashboardLayout,
    dashboardCharts,

    isProcessing: streamProcessor.isProcessing,
    isDone: streamProcessor.isDone,
    isCanceled: streamProcessor.isCanceled,
    isPending: streamProcessor.isPending,

    sendMessageToAgent,
    cancelAgentResponse,
    $reset,
  };
});
