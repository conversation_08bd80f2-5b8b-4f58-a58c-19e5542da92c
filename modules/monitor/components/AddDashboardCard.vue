<template>
  <Card
    class="group relative h-64 border-2 border-dashed border-gray-300 cursor-pointer hover:border-theme-color transition-all duration-200 hover:shadow-lg"
    @click="createDashboard"
  >
    <CardContent
      class="flex flex-col items-center justify-center h-full text-gray-500 group-hover:text-theme-color transition-colors duration-200 p-6"
    >
      <div
        class="w-12 h-12 mb-4 flex items-center justify-center rounded-full bg-gray-100 group-hover:bg-theme-color/10 transition-colors duration-200"
      >
        <PlusIcon class="w-6 h-6" :stroke-width="2.5" />
      </div>
      <span class="text-sm font-medium">Add New Dashboard</span>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Card, CardContent } from "@/components/ui/card";
import { PlusIcon } from "lucide-vue-next";

defineProps<{ createDashboard: () => void }>();
</script>
