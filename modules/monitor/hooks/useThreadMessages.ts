import { getMastraClient } from "~/hooks/agent/helper";
import { coreMessageToClientMessage } from "~/hooks/agent/message";
import type { Message } from "~/types/message";
import { useMonitorDashboard } from "./useMonitorDashboard";

export function useThreadMessages(): Ref<Message[] | null> {
  const { dashboard } = useMonitorDashboard();

  const threadId = computed(() => dashboard.value?.threadId);

  const { data } = useAsyncData<Message[]>(
    "monitor-dashboard-thread-messages",
    async () => {
      const client = getMastraClient();
      if (!threadId.value) {
        return [] as Message[];
      }

      const thread = await client.getMemoryThread(
        threadId.value,
        "monitorAgent"
      );
      const { messages } = await thread.getMessages();
      return coreMessageToClientMessage(messages);
    },
    {
      server: false,
      watch: [threadId],
    }
  );

  return data;
}
