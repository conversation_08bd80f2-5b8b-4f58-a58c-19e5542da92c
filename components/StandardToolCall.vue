<script setup lang="ts">
import { ref, nextTick, watchEffect, computed, onUnmounted } from "vue";
import {
  ChevronRightIcon,
  ShovelIcon,
  ListIcon,
  ScanSearchIcon,
  MessageCircleIcon,
  PackageSearchIcon,
  ReceiptTextIcon,
  GitCompareIcon,
  EyeIcon,
  WrenchIcon,
  NetworkIcon,
  ScanTextIcon,
  LayoutGridIcon,
  BoxIcon,
  BoxesIcon,
  LineChartIcon,
  PlayIcon,
} from "lucide-vue-next";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-json";
import { formatElapsedTime } from "~/utils/format";
import CopyButton from "./CopyButton.vue";
import ParalleloScan from "./ParalleloScan.vue";
import { useTimer } from "~/hooks/useTimer";

const isOpen = ref(false);
const inputRef = ref<HTMLElement | null>(null);
const outputRef = ref<HTMLElement | null>(null);

const { startTimer, stopTimer, currentElapsedTime, timer } = useTimer();

interface Props {
  name: string;
  input: Record<string, unknown>;
  output?: Record<string, unknown>;
  elapsedTime?: number;
  isHistoryAnalysis?: boolean;
  isRunning?: boolean;
  status?: string;
}

const props = withDefaults(defineProps<Props>(), {
  output: undefined,
  elapsedTime: undefined,
  isHistoryAnalysis: false,
  status: undefined,
});

const isRunning = computed(() => props.isRunning);

const formatJSON = (obj: Record<string, unknown>) => {
  return JSON.stringify(obj, null, 2);
};

const isProcessing = computed(() => {
  return props.output === undefined && isRunning.value;
});

const displayElapsedTime = computed(() => {
  if (isProcessing.value) {
    return currentElapsedTime.value;
  }
  return props.elapsedTime ?? 0;
});

watchEffect(() => {
  if (isProcessing.value && !timer.value) {
    startTimer();
  } else if (!isProcessing.value && timer.value) {
    stopTimer();
  }
});

watchEffect(() => {
  if (!isRunning.value) {
    stopTimer();
  }
});

onUnmounted(() => {
  stopTimer();
});

const emit = defineEmits(["tool-height-change"]);

watchEffect(() => {
  if (isOpen.value) {
    nextTick(() => {
      if (inputRef.value) {
        Prism.highlightElement(inputRef.value.querySelector("code")!);
      }
      if (outputRef.value) {
        Prism.highlightElement(outputRef.value.querySelector("code")!);
      }
    });
  }
});

watch(isOpen, () => {
  emit("tool-height-change");
});

const toolIconMap = {
  drillDownTool: ShovelIcon,
  askUserTool: MessageCircleIcon,
  listEntitiesTool: ListIcon,
  searchLinksTool: ScanSearchIcon,
  lookupCluesTool: PackageSearchIcon,
  clueDetailTool: ReceiptTextIcon,
  comparisonTool: GitCompareIcon,
  inspectEntityTool: EyeIcon,
  genTopologyTool: NetworkIcon,
  retrieveProcessTool: ScanTextIcon,
  updateLayoutTool: LayoutGridIcon,
  updateChartTool: LineChartIcon,
  "capehorn_list-cubes": BoxesIcon,
  "capehorn_describe-cubes": BoxIcon,
  "capehorn_run-cube-query": PlayIcon,
} as const;

const toolDisplayNameMap = {
  listEntitiesTool: "View Entities",
  searchLinksTool: "Search Links",
  lookupCluesTool: "Find Clues",
  clueDetailTool: "View Clue Detail",
  inspectEntityTool: "Inspect Entity",
  askUserTool: "Ask User",
  genTopologyTool: "Visualize Topology",
  retrieveProcessTool: "Retrieve Process",
  updateLayoutTool: "Update Layout",
  updateChartTool: "Update Chart",
  "capehorn_list-cubes": "List Cubes",
  "capehorn_describe-cubes": "Describe Cubes",
  "capehorn_run-cube-query": "Run Cube Query",
} as const;

const removeUnderscores = (value: unknown) => {
  return String(value).replace(/_/g, " ");
};

const getToolIcon = (toolName: string) => {
  return toolIconMap[toolName as keyof typeof toolIconMap] || WrenchIcon;
};
const getToolDisplayName = (
  toolName: string,
  input?: Record<string, unknown>
) => {
  if (toolName === "drillDownTool") {
    if (input && input.metric && input.dimension) {
      return `Drill down ${removeUnderscores(
        input.dimension
      )} by ${removeUnderscores(input?.metric)}`;
    } else if (input && input.metric) {
      return `Calculate ${removeUnderscores(input.metric)}`;
    }
    return "Drill Down";
  }

  if (toolName === "comparisonTool") {
    if (input && input.metric) {
      return `Compare ${removeUnderscores(input.metric)} of the last 24 hours`;
    }
    return "Compare with Time";
  }

  return (
    toolDisplayNameMap[toolName as keyof typeof toolDisplayNameMap] || toolName
  );
};
</script>

<template>
  <Collapsible v-model:open="isOpen" :unmount-on-hide="false">
    <div class="flex items-center text-xs text-[#bfbfbf]">
      <ParalleloScan :animated="isHistoryAnalysis ? false : isProcessing">
        <div
          variant="ghost"
          size="sm"
          class="justify-start flex border border-title-bg rounded-md-new bg-title-bg h-6 leading-6"
        >
          <component
            :is="getToolIcon(props.name)"
            class="size-3.5 mx-2 self-center"
          />
          <CollapsibleTrigger>
            <div class="flex items-center cursor-pointer gap-x-1 mr-1">
              {{ getToolDisplayName(props.name, props.input) }}

              <span @click.stop="isOpen = !isOpen">
                <ChevronRightIcon
                  class="size-4 transition-transform duration-200 self-center"
                  :class="{ 'rotate-90': isOpen }"
                />
              </span>
            </div>
          </CollapsibleTrigger></div
      ></ParalleloScan>
      <div v-if="!isHistoryAnalysis" class="flex items-center gap-x-2 ml-4">
        <div
          v-if="isProcessing || elapsedTime !== undefined"
          class="flex items-center gap-1"
        >
          {{ formatElapsedTime(displayElapsedTime) }}
        </div>
      </div>
    </div>
    <CollapsibleContent
      :class="{ 'p-2 bg-title-bg mt-3 rounded-md-new ': isOpen }"
    >
      <div class="flex flex-col space-y-2 text-xs text-default-text">
        <div class="flex flex-col rounded-lg bg-title-bg overflow-hidden">
          <div
            class="flex items-center justify-between p-1 font-medium pl-3 bg-white"
          >
            <div>Parameters:</div>
            <CopyButton :copy-data="props.input" :status="props.status" />
          </div>
          <div class="border-t-1 border-[#d9d9d9] w-full" />
          <pre
            ref="inputRef"
            class="p-2 !my-0 overflow-x-auto language-json max-h-96 overflow-y-auto !bg-white"
          ><code class="language-json  ">{{ formatJSON(props.input) }}</code></pre>
        </div>
        <div
          v-if="props.output"
          class="flex flex-col rounded-lg overflow-hidden"
        >
          <div
            class="flex items-center justify-between p-1 font-medium pl-3 bg-white"
          >
            <div>Result:</div>
            <CopyButton :copy-data="props.output" :status="props.status" />
          </div>
          <div class="border-t-1 border-[#d9d9d9] w-full" />
          <pre
            ref="outputRef"
            class="p-2 !my-0 overflow-x-auto language-json max-h-96 overflow-y-auto !bg-white"
          ><code class="language-json  ">{{ formatJSON(props.output) }}</code></pre>
        </div>
      </div>
    </CollapsibleContent>
  </Collapsible>
</template>
<style scoped lang="scss">
.in-process {
  background-color: #e2ecf2;
  color: #3a7ca5;
  border-color: #e2ecf2;
}
</style>
