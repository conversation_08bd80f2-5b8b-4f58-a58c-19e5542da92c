<script setup lang="ts">
import { Bar } from "vue-chartjs";
import type { ChartData, TooltipItem } from "chart.js";
import type { BarLineData } from "../types";
import { computed } from "vue";
import { getBarChartData } from "../helpers/chartData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { getValueFormatter } from "~/utils/unit";
import type { UnitNames } from "~/utils/unit";

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const props = withDefaults(
  defineProps<{
    data: BarLineData[];
    unit: string;
    title: string;
    chartHeight?: string;
  }>(),
  {
    chartHeight: "",
  }
);

const chartData = computed(
  () => getBarChartData(props.data) as ChartData<"bar", number[], unknown>
);

// 图表配置选项
const chartOptions = computed(() => ({
  indexAxis: "y", // 水平分组条形图
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: false,
      grid: {
        display: true,
        color: "rgba(0, 0, 0, 0.1)",
      },
      ticks: {
        color: "#141414",
        maxTicksLimit: 8,
        callback: (tickValue: string | number) => {
          const formatter = getValueFormatter(props.unit as UnitNames);
          const result = formatter(Number(tickValue));
          const value =
            typeof result.value === "number"
              ? result.value.toFixed(2)
              : result.value;
          return `${value} ${result.unit}`;
        },
      },
    },
    y: {
      stacked: false,
      grid: {
        display: false,
      },
      ticks: {
        color: "#141414",
        font: {
          size: 12,
        },
      },
    },
  },
  plugins: {
    title: {
      display: true,
      text: props.title,
      align: "start",
      padding: { bottom: 40, top: 10 },
      font: { weight: "bold", size: 16 },
    },
    legend: {
      display: true,
      position: "bottom",
    },

    tooltip: {
      mode: "index",
      intersect: false,
      backgroundColor: "#fff",
      titleColor: "black",
      bodyColor: "black",
      callbacks: {
        labelTextColor: (ctx: TooltipItem<"bar">) => {
          const dataset = ctx.dataset;
          return Array.isArray(dataset.borderColor)
            ? dataset.borderColor[0]
            : dataset.borderColor;
        },
        label: function (context: TooltipItem<"bar">) {
          const label = context.dataset.label || "";
          const formatter = getValueFormatter(props.unit as UnitNames);
          const result = formatter(Number(context.parsed.x));

          const value =
            typeof result.value === "number"
              ? result.value.toFixed(2)
              : result.value;

          return `${label}: ${value}${result.unit || ""}`;
        },
      },
      borderColor: "rgba(156, 163, 175, 0.5)",
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
    },
  },
  interaction: {
    mode: "nearest",
    axis: "y",
    intersect: false,
  },
  elements: {
    bar: {
      borderWidth: 0,
      borderRadius: 2,
    },
  },
}));
</script>

<template>
  <Bar
    :data="chartData"
    :options="chartOptions"
    :style="
      props.chartHeight ? { height: props.chartHeight } : { height: '100%' }
    "
  />
</template>
