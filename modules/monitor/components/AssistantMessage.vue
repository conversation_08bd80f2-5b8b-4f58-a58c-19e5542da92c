<script setup lang="ts">
import type { AssistantMessage, ToolCallMessage } from "~/types/message";

const { message } = defineProps<{
  message: AssistantMessage;
}>();
</script>

<template>
  <div v-for="(content, contentIndex) in message.content" :key="contentIndex">
    <div v-if="content.type === 'text'">
      <Markdown :source="content.text" class="prose-sm" />
    </div>
    <div v-if="content.type === 'tool-call'" class="my-2">
      <StandardToolCall
        :name="(content as ToolCallMessage).toolName"
        :input="(content as ToolCallMessage).args"
        :output="(content as ToolCallMessage).result"
      />
    </div>
  </div>
</template>
