<script setup lang="ts">
import { GridLayout } from "grid-layout-plus";
import { DateTimeRangePicker } from "@/components/ui/datetime-range-picker";
import dayjs from "dayjs";
import { useMonitorDashboard } from "~/modules/monitor/hooks/useMonitorDashboard";
import type { Chart } from "~/modules/monitor/types";
import DashboardPanel from "~/modules/monitor/components/DashboardPanel.vue";
import PlaceholderPanel from "~/modules/monitor/components/PlaceholderPanel.vue";

definePageMeta({
  layout: "monitor",
});

const { dashboard } = useMonitorDashboard();
const dashboardLayout = computed(
  () => dashboard.value?.dashConfig.layout ?? []
);
const dashboardCharts = computed(
  () => dashboard.value?.dashConfig.charts ?? {}
);
const timeRange = ref<{ start: Date; end: Date }>({
  start: dayjs().subtract(7, "day").startOf("day").toDate(),
  end: dayjs().toDate(),
});

const charts = computed(() => {
  const newCharts: Record<string, Chart> = {};
  for (const [id, config] of Object.entries(dashboardCharts.value)) {
    if (config.query.timeDimensions) {
      newCharts[id] = {
        ...config,
        query: {
          ...config.query,
          timeDimensions: [
            {
              ...config.query.timeDimensions[0],
              dateRange: [
                timeRange.value.start.toISOString(),
                timeRange.value.end.toISOString(),
              ],
            },
          ],
        },
      };
    } else {
      const tableName = (
        config.query.dimensions?.[0] ?? config.query.measures?.[0]
      )?.split(".")[0];
      if (tableName) {
        newCharts[id] = {
          ...config,
          query: {
            ...config.query,
            timeDimensions: [
              {
                dimension: `${tableName}.timestamp`,
                dateRange: [
                  timeRange.value.start.toISOString(),
                  timeRange.value.end.toISOString(),
                ],
              },
            ],
          },
        };
      } else {
        newCharts[id] = { ...config };
      }
    }
  }
  return newCharts;
});

useHead({
  title: `${dashboard?.value?.name ?? ""} - Monitor`,
});
</script>

<template>
  <div class="p-3 w-full h-full">
    <ClientOnly>
      <Teleport to="#page-header-left">
        <div class="px-5">Monitor - {{ dashboard?.name }}</div>
      </Teleport>
      <Teleport to="#page-header-right">
        <div class="flex justify-end">
          <DateTimeRangePicker v-model:value="timeRange" />
        </div>
      </Teleport>
    </ClientOnly>

    <GridLayout
      v-model:layout="dashboardLayout"
      :col-num="12"
      :row-height="100"
      :is-draggable="false"
      :is-resizable="false"
      vertical-compact
      use-css-transforms
    >
      <template #item="{ item }">
        <DashboardPanel v-if="charts[item.i]" :config="charts[item.i]" />
        <PlaceholderPanel v-else />
      </template>
    </GridLayout>
  </div>
</template>
