import type { Query, ResultSet } from "@cubejs-client/core";

export interface TableDataItem {
  [key: string]: string | number | string[] | undefined;
}

export interface CancellableRequest {
  promise: Promise<ResultSet>;
  cancel: () => void;
}

export type ChartType = "line" | "bar" | "table";

export interface ChartResult {
  type: ChartType;
  data: ChartDataType;
  unit?: string;
  measuresData?: string[];
  measureUnits?: Record<string, string>;
}

export type BarLineData = {
  label: string | number;
  value: string | number;
  category: string;
};

export type ChartDataType = BarLineData[] | TableDataItem[];

export type Panel = {
  x: number;
  y: number;
  w: number;
  h: number;
  i: string;
};

export type Chart = {
  id: string;
  title: string;
  type: string;
  query: Query;
};

export type DashboardLayout = Panel[];

export type DashboardConfig = {
  layout: Array<Panel>;
  charts: Record<string, Chart>;
};

export type Dashboard = {
  id: number;
  name: string;
  description?: string;
  thumbnailConfig: Record<string, unknown>; // TODO: Add schema
  dashConfig: DashboardConfig;
  createdAt: Date;
  updatedAt: Date;
  owner: string;
  threadId: string;
};
