{"id": "556a98f9-4b11-48cc-9bb5-ff4234db26c0", "prevId": "537be673-18f6-4575-9618-626f5c0e03ac", "version": "7", "dialect": "postgresql", "tables": {"capehorn_app.dashboard": {"name": "dashboard", "schema": "capehorn_app", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "dashboard_id_seq", "schema": "capehorn_app", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "thumbnail_config": {"name": "thumbnail_config", "type": "jsonb", "primaryKey": false, "notNull": true}, "dash_config": {"name": "dash_config", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "owner": {"name": "owner", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"capehorn_app": "capehorn_app"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}