import { and, eq } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardTable } from "~/server/db/schema";
import { validateNumericId } from "~/server/utils/validation";

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, "id");
    const dashboardId = validateNumericId(id, "Dashboard ID");

    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const [dashboard] = await db
      .select()
      .from(dashboardTable)
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      );

    if (!dashboard) {
      throw createError({
        statusCode: 404,
        statusMessage: "Dashboard not found",
      });
    }

    return dashboard;
  } catch (error) {
    console.error("Error fetching dashboard:", error);

    if (error instanceof Error && "statusCode" in error) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: "Failed to fetch dashboard",
    });
  }
});
