<script setup lang="ts">
import { useMonitorEditStore } from "../stores/monitorEditStore";
import MessageInput from "./MessageInput.vue";
import Messages from "./Messages.vue";

const monitorStore = useMonitorEditStore();

const sendMessageToAgent = (message: string) => {
  monitorStore.sendMessageToAgent(message);
};
</script>

<template>
  <div class="min-w-96 xl:min-w-[650px] h-full border-r flex flex-col">
    <Messages />
    <div class="px-2 pb-2">
      <MessageInput @submit="sendMessageToAgent" />
    </div>
  </div>
</template>
