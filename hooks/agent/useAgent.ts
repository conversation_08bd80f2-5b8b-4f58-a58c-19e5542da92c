import { RuntimeContext } from "@mastra/core/di";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { nanoid } from "nanoid";
import { onUnmounted, ref } from "vue";
import { toast } from "vue-sonner";
import { UserStore } from "~/lib/userStore";
import type { Message } from "~/types/message";
import { getMastraClient } from "./helper";
import type { StreamProcessorCreator } from "./types";

dayjs.extend(timezone);
dayjs.extend(utc);

const DEFAULT_CUSTOM_API_ENDPOINT = "/agent/capehorn/api";

export type AgentRuntimeContext = Record<string, unknown>;

export type ReadImageRuntimeContext = {
  rawImageUrl: string;
  question: string;
};

export const getDefaultRuntimeContext = () => {
  const runtimeContextInstance = new RuntimeContext<AgentRuntimeContext>();
  runtimeContextInstance.set("datetime", dayjs().format("YYYY-MM-DD HH:mm:ss"));
  runtimeContextInstance.set("timezone", dayjs.tz.guess());
  runtimeContextInstance.set("locale", "en-US");

  const userSettings = UserStore.getSettings();
  if (userSettings.systemPrompt) {
    runtimeContextInstance.set("systemPrompt", userSettings.systemPrompt);
  }
  if (userSettings.userMemories) {
    runtimeContextInstance.set("userMemories", userSettings.userMemories);
  }

  return runtimeContextInstance;
};

export const useAgent = (
  agentName: string,
  streamProcessorCreator: StreamProcessorCreator<unknown, unknown>,
  initialThreadId?: string,
  beforeAgentStart?: () => Promise<void>
) => {
  let abortController: AbortController | null = null;

  const threadId = ref<string>(initialThreadId || `thread-${nanoid()}`);
  const runId = ref<string>("");
  const runtimeContext = ref<AgentRuntimeContext>();
  const toolCallTimings = ref<
    Record<string, { startTime: number; duration?: number; toolName: string }>
  >({});
  const totalToolCallTime = ref<number>(0);

  const resourceId = useCookie("resourceId");
  const streamProcessor = streamProcessorCreator({
    onAbort: (reason: string) => {
      abortController?.abort(reason);
    },
  });

  const start = async (userMessage: string, context: AgentRuntimeContext) => {
    try {
      streamProcessor.reset({});

      await beforeAgentStart?.();
      await nextTick();

      runtimeContext.value = context;
      runId.value = `run-${nanoid()}`;

      const userMsg: Message = {
        role: "user",
        content: [{ type: "text", text: userMessage }],
      };

      streamProcessor.emitEvent({
        type: "update-process-status",
        payload: {
          status: "pending",
        },
      });

      streamProcessor.emitEvent({
        type: "user-message",
        payload: {
          message: userMessage,
        },
      });

      abortController = new AbortController();
      const client = getMastraClient(abortController?.signal);
      const agent = client.getAgent(agentName);

      const runtimeContextInstance = getDefaultRuntimeContext();
      Object.entries(context ?? {}).forEach(([key, value]) => {
        runtimeContextInstance.set(key as keyof AgentRuntimeContext, value);
      });

      const response = await agent.stream({
        messages: [userMsg],
        runtimeContext: runtimeContextInstance,
        threadId: threadId.value,
        runId: runId.value,
        resourceId: resourceId.value ?? undefined,
        savePerStep: true,
      });

      if (!response.body) {
        throw new Error("No response body");
      }

      streamProcessor.setState({ status: "pending" });

      await response.processDataStream({
        onStartStepPart: () => {
          streamProcessor.emitEvent({
            type: "step-start",
          });
        },
        onFinishStepPart: () => {
          streamProcessor.emitEvent({
            type: "step-finish",
          });
        },
        onTextPart: (value) => {
          streamProcessor.emitEvent({
            type: "text",
            payload: { text: value },
          });
        },
        onToolCallPart: (value) => {
          toolCallTimings.value[value.toolCallId] = {
            startTime: Date.now(),
            toolName: value.toolName,
          };

          streamProcessor.emitEvent({
            type: "tool-call",
            payload: {
              toolCallId: value.toolCallId,
              toolName: value.toolName,
              args: value.args,
            },
          });
        },
        onToolResultPart: (value) => {
          let elapsedTime = 0;
          if (toolCallTimings.value[value.toolCallId]) {
            elapsedTime =
              Date.now() - toolCallTimings.value[value.toolCallId].startTime;
            toolCallTimings.value[value.toolCallId].duration = elapsedTime;
            totalToolCallTime.value += elapsedTime;
          }

          streamProcessor.setState({ status: "analyzing" });

          streamProcessor.emitEvent({
            type: "tool-result",
            payload: {
              toolCallId: value.toolCallId,
              result: value.result,
              elapsedTime,
            },
          });
        },
        // onFinishMessagePart: () => {
        //   throw new Error("finish message, stop process");
        // },
        onErrorPart: (error) => {
          streamProcessor.emitEvent({
            type: "error",
            payload: { error },
          });
          throw new Error(error);
        },
      });
      streamProcessor.emitEvent({
        type: "stream-finish",
      });
      streamProcessor.setState({ status: "done" });
    } catch (error) {
      console.error("Agent error", error);
      if (error instanceof Error && error.name === "AbortError") {
        console.log(error.message);
        return;
      }
      if (
        error instanceof Error &&
        error.message === "finish message, stop process"
      ) {
        return;
      }
      toast.error("Error", {
        description: error instanceof Error ? error.message : "Unknown error",
      });
    }
  };

  const confirmInteractiveResult = async (
    toolName: string,
    userInputs: string[]
  ) => {
    await $fetch(`${DEFAULT_CUSTOM_API_ENDPOINT}/interactive`, {
      method: "PUT",
      body: {
        threadId: threadId.value,
        runId: runId.value,
        toolName: toolName,
        userInputs: userInputs,
      },
    });

    streamProcessor.setState({ status: "analyzing" });
  };

  onUnmounted(() => {
    abortController?.abort();
    streamProcessor.cleanup();
  });

  const cancel = () => {
    abortController?.abort("cancel agent response");
    streamProcessor.setState({ status: "canceled" });
  };

  const reset = (newThreadId?: string) => {
    abortController?.abort();
    streamProcessor.reset({
      status: "idle",
    });
    runtimeContext.value = undefined;
    if (newThreadId) {
      threadId.value = newThreadId;
      resourceId.value = resourceId.value ?? undefined;
    }
    toolCallTimings.value = {};
    totalToolCallTime.value = 0;
  };

  const setThreadId = (newThreadId: string) => {
    threadId.value = newThreadId;
    resourceId.value = resourceId.value ?? undefined;
  };

  return {
    // state
    threadId,
    resourceId,
    runId,

    // objects
    streamProcessor,

    // actions
    start,
    cancel,
    reset,
    setThreadId,
    confirmInteractiveResult,
  };
};
