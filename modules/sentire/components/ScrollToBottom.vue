<script setup lang="ts">
import { ChevronDownIcon } from "lucide-vue-next";
import { ref, onMounted, onUnmounted } from "vue";

const { isRunning } = defineProps<{
  isRunning?: boolean;
}>();

const isVisible = ref(false);

const emit = defineEmits<{
  (e: "to-bottom"): void;
}>();

const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: "smooth",
  });
  emit("to-bottom");
};

const handleVisible = () => {
  const isNearBottom =
    window.innerHeight + window.scrollY >= document.body.offsetHeight - 100;
  isVisible.value = !isNearBottom;
};

let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  window.addEventListener("scroll", handleVisible);
  handleVisible();
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        handleVisible();
      });
    });
    resizeObserver.observe(document.body);
  }
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleVisible);
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<template>
  <div
    class="absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
    }"
    @click="scrollToBottom"
  >
    <!-- Button with animated border -->
    <div class="relative">
      <!-- Main circular button with frosted glass effect -->
      <div
        class="w-9 h-9 rounded-full flex items-center justify-center relative overflow-hidden glass-button"
        :class="{
          'shadow-lg': !isRunning,
          'shadow-xl': isRunning && isVisible,
        }"
      >
        <!-- Full circle border (when not running) -->
        <div
          v-if="!isRunning || !isVisible"
          class="absolute inset-0 rounded-full border-2 border-white/40"
        />

        <!-- Animated quarter circle border (when running) -->
        <div
          v-if="isRunning && isVisible"
          class="absolute inset-0"
        >
          <div class="quarter-circle-border animate-spin" />
        </div>

        <!-- Stationary downward arrow -->
        <ChevronDownIcon :size="16" class="text-gray-700 drop-shadow-sm" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 磨玻璃效果 */
.glass-button {
  /* 半透明白色背景 */
  background: rgba(255, 255, 255, 0.25);

  /* 磨玻璃模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* 边框增强玻璃质感 */
  border: 1px solid rgba(255, 255, 255, 0.3);

  /* 渐变背景增加深度 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.glass-button:hover {
  background: rgba(255, 255, 255, 0.35);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* 激活状态 */
.glass-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
}

.quarter-circle-border {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: #3a7ca5; /* Theme color for the animated segment */
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
