<script setup lang="ts">
import { ChevronDownIcon } from "lucide-vue-next";
import { ref, onMounted, onUnmounted } from "vue";

const { isRunning } = defineProps<{
  isRunning?: boolean;
}>();

const isVisible = ref(false);

const emit = defineEmits<{
  (e: "to-bottom"): void;
}>();

const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: "smooth",
  });
  emit("to-bottom");
};

const handleVisible = () => {
  const isNearBottom =
    window.innerHeight + window.scrollY >= document.body.offsetHeight - 100;
  isVisible.value = !isNearBottom;
};

let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  window.addEventListener("scroll", handleVisible);
  handleVisible();
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        handleVisible();
      });
    });
    resizeObserver.observe(document.body);
  }
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleVisible);
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<template>
  <div
    class="absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
    }"
    @click="scrollToBottom"
  >
    <!-- Button with animated border -->
    <div class="relative">
      <!-- Main circular button with frosted glass effect -->
      <div
        class="w-9 h-9 rounded-full flex items-center justify-center relative overflow-hidden glass-button"
      >
        <!-- Full circle border (when not running) -->
        <div
          v-if="!isRunning || !isVisible"
          class="absolute inset-0 rounded-full border-2 border-white/40"
        />

        <!-- Animated light trail effect (when running) -->
        <div
          v-if="isRunning && isVisible"
          class="absolute inset-0"
        >
          <div class="light-trail-container animate-spin">
            <!-- Main light point -->
            <div class="light-point" />
            <!-- Trail segments with decreasing opacity -->
            <div class="trail-segment trail-1" />
            <div class="trail-segment trail-2" />
            <div class="trail-segment trail-3" />
            <div class="trail-segment trail-4" />
          </div>
        </div>

        <!-- Stationary downward arrow -->
        <ChevronDownIcon :size="16" class="text-gray-700 drop-shadow-sm" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 磨玻璃效果 */
.glass-button {
  /* 半透明白色背景 */
  background: rgba(255, 255, 255, 0.25);

  /* 磨玻璃模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* 边框增强玻璃质感 */
  border: 1px solid rgba(255, 255, 255, 0.3);

  /* 渐变背景增加深度 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );

  /* 柔和的浮动立体效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.glass-button:hover {
  background: rgba(255, 255, 255, 0.35);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  /* 悬停时增强阴影效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 激活状态 */
.glass-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
  /* 按下时减少阴影，模拟按下效果 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 光点拖拽残影效果 */
.light-trail-container {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  animation: spin 2s linear infinite;
  will-change: transform;
}

/* 主光点 */
.light-point {
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #3a7ca5;
  box-shadow:
    0 0 8px rgba(58, 124, 165, 0.8),
    0 0 16px rgba(58, 124, 165, 0.4),
    0 0 24px rgba(58, 124, 165, 0.2);
  animation: pulse-glow 1.5s ease-in-out infinite alternate;
}

/* 残影轨迹段 */
.trail-segment {
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #3a7ca5;
}

/* 残影位置和透明度递减 */
.trail-1 {
  transform: translateX(-50%) rotate(-15deg) translateY(18px) rotate(15deg);
  opacity: 0.6;
  box-shadow: 0 0 6px rgba(58, 124, 165, 0.3);
}

.trail-2 {
  transform: translateX(-50%) rotate(-30deg) translateY(18px) rotate(30deg);
  opacity: 0.4;
  box-shadow: 0 0 4px rgba(58, 124, 165, 0.2);
}

.trail-3 {
  transform: translateX(-50%) rotate(-45deg) translateY(18px) rotate(45deg);
  opacity: 0.25;
  box-shadow: 0 0 3px rgba(58, 124, 165, 0.15);
}

.trail-4 {
  transform: translateX(-50%) rotate(-60deg) translateY(18px) rotate(60deg);
  opacity: 0.1;
  box-shadow: 0 0 2px rgba(58, 124, 165, 0.1);
}

/* 光点脉冲效果 */
@keyframes pulse-glow {
  0% {
    transform: translateX(-50%) scale(1);
    box-shadow:
      0 0 8px rgba(58, 124, 165, 0.8),
      0 0 16px rgba(58, 124, 165, 0.4),
      0 0 24px rgba(58, 124, 165, 0.2);
  }
  100% {
    transform: translateX(-50%) scale(1.1);
    box-shadow:
      0 0 12px rgba(58, 124, 165, 1),
      0 0 20px rgba(58, 124, 165, 0.6),
      0 0 32px rgba(58, 124, 165, 0.3);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
