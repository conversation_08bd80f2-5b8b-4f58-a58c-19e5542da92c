<template>
  <Card
    :class="[
      '!p-0 group relative h-64 cursor-pointer hover:shadow-lg hover:border-theme-color transition-all duration-200 flex flex-col',
    ]"
    @click="handleCardClick"
  >
    <CardHeader class="border-b border-gray-200 flex-shrink-0 !p-0">
      <CardDescription class="flex items-center gap-2 mt-1 px-2 py-1">
        <div
          class="m-1 flex items-center text-red-400 bg-red-100 rounded-xl gap-1 align-middle px-2 py-1"
        >
          <TriangleAlertIcon class="w-4 h-4" />
          <span class="text-sm leading-none flex items-center">1</span>
        </div>
        <span class="text-base font-semibold">{{ dashboard.name }}</span>
        <span class="ml-auto flex items-center">
          <slot name="actions" />
        </span>
      </CardDescription>
    </CardHeader>
    <CardContent class="flex-1 flex items-center justify-center p-4">
      <div class="text-center text-gray-400">
        <span class="text-xs">图表区域</span>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import {
  Card,
  CardContent,
  CardHeader,
  CardDescription,
} from "@/components/ui/card";
import { TriangleAlertIcon } from "lucide-vue-next";

interface Dashboard {
  id: number;
  name: string;
}

const props = defineProps<{
  dashboard: Dashboard;
  onCardClick: () => void;
}>();

const handleCardClick = () => {
  props.onCardClick();
};
</script>
