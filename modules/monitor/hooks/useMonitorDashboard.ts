import type { Dashboard } from "../types";

export const useMonitorDashboard = () => {
  const route = useRoute();
  const id = computed(() => route.params.id as string);

  const { data } = useAsyncData<Dashboard | null>(
    "dashboard",
    () => {
      if (!id.value) {
        return Promise.resolve(null);
      }

      const headers = useRequestHeaders(["cookie"]);
      return $fetch(`/api/dashboard/${id.value}`, {
        headers,
      }) as Promise<Dashboard>;
    },
    {
      watch: [id],
      server: false,
    }
  );

  return {
    dashboard: data,
  };
};
