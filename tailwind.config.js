/** @type {import('tailwindcss').Config} */
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: "#1E40AF",
        brand: {
          light: "#3AB0FF",
          DEFAULT: "#0080FF",
          dark: "#0055AA",
        },
      },
      borderRadius: {
        'none': 'var(--radius-none)',
        'xs': 'var(--radius-xs)',
        'sm-new': 'var(--radius-sm-new)',
        'md-new': 'var(--radius-md-new)',
        'lg-new': 'var(--radius-lg-new)',
        'lg-plus': 'var(--radius-lg-plus)',
        'xl-new': 'var(--radius-xl-new)',
        'xl-plus': 'var(--radius-xl-plus)',
        '2xl-new': 'var(--radius-2xl)',
        'full': 'var(--radius-full)',
      }
    },
  },
};
