import {
  integer,
  jsonb,
  pgSchema,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const capehornSchema = pgSchema("capehorn_app");

export const dashboardTable = capehornSchema.table("dashboard", {
  id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  thumbnailConfig: jsonb("thumbnail_config").notNull(),
  dashConfig: jsonb("dash_config").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  owner: varchar("owner", { length: 255 }).notNull(),
  threadId: varchar("thread_id", { length: 255 }).notNull(),
});
