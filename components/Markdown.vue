<script setup lang="ts">
import { reactiveOmit } from "@vueuse/core";
import { cn } from "@/lib/utils";
import { onMounted, nextTick, ref, watch, h, reactive } from "vue";
import Prism from "prismjs";
import { marked, type Token } from "marked";
import morphdom from "morphdom";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-bash";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.css";
import type { Mermaid } from "mermaid";

defineOptions({
  name: "MarkdownRenderer",
});

function simpleHash(str: string) {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
    hash = (hash << 5) + hash + str.charCodeAt(i);
  }
  return (hash >>> 0).toString(16);
}

const props = defineProps<{
  source: string;
  class?: string | Record<string, boolean>;
  style?: string;
}>();
const delegatedProps = reactiveOmit(props, "class", "source");
const markdownRef = ref<HTMLElement>();
let mermaidInstance: Mermaid | null = null;
const mermaidGraphIdMap = new Map<string, string>();

const SVG_ELEMENTS = [
  "svg",
  "g",
  "style",
  "rect",
  "marker",
  "foreignObject",
  "path",
  "circle",
  "polygon",
  "line",
  "polyline",
  "ellipse",
  "text",
  "tspan",
  "defs",
  "clipPath",
  "mask",
  "use",
];

// Helper function to process dynamic class
const processClass = (classValue?: string | Record<string, boolean>) => {
  if (!classValue) return undefined;

  if (typeof classValue === "string") {
    return classValue;
  }

  // Handle object format: { "class-name": boolean }
  return Object.entries(classValue)
    .filter(([, value]) => value)
    .map(([key]) => key)
    .join(" ");
};

const classes = cn(
  "prose prose-stone min-w-full prose-code:text-[#24292f] prose-strong:text-[#24292f] prose-code:before:content-[''] prose-code:after:content-['']",
  processClass(props.class)
);

const mdExtension = {
  async: true,
  async walkTokens(token: Token) {
    try {
      if (token.type === "code") {
        const { lang } = token;
        if (lang === "mermaid") {
          const codeBlockPattern = /```mermaid\s*[\s\S]*?\n```/g;
          const match = token.raw.match(codeBlockPattern);
          const id = `mermaid-graph-${simpleHash(token.text)}`;
          if (match) {
            if (mermaidGraphIdMap.has(id)) {
              token.text = mermaidGraphIdMap.get(id)!;
            } else {
              if (token.text.startsWith("<svg")) {
                return;
              }
              const { svg } = (await mermaidInstance?.render(
                id,
                token.text
              )) as {
                svg: string;
              };
              token.text = svg;
              mermaidGraphIdMap.set(id, svg);
            }
          } else {
            token.text = "Loading...";
          }
        }
      }
    } catch (e) {
      console.error(e);
      return;
    }
  },
};

const renderer = new marked.Renderer();
renderer.code = function ({ text, lang }) {
  if (lang === "mermaid") {
    return `<div class="mermaid-diagram rounded-xl ">${text}</div>`;
  }
  return `<pre><code class="language-${lang}">${text}</code></pre>`;
};
renderer.image = function ({ href, text }) {
  let imageDescription = `<p class="-mt-8">${text}</p>`;
  if (text.toLowerCase() === "image" || text.toLowerCase() === "chart") {
    imageDescription = "";
  }
  return `
  <div class="flex flex-col items-center justify-center transition-all rounded-xl border px-4 py-2 shadow-lg">
    <img src="${href}" alt="${text}">
    ${imageDescription}
  </div>`;
};
renderer.link = function ({ href, text }) {
  // 检查是否是 cube:// 协议的链接
  if (href.startsWith("cube://")) {
    try {
      const encodedQuery = href.replace("cube://", "");

      const placeholder = `<div class="cube-chart-placeholder" data-title="${text}" data-cube-query="${encodedQuery}">Loading cube chart: ${text}...</div>`;
      return placeholder;
    } catch (error) {
      console.error("Failed to parse cube query:", error);
      return `<a href="${href}">${text}</a>`;
    }
  }

  return `<a href="${href}" target="_blank" rel="noopener noreferrer">${text}</a>`;
};

marked.use(mdExtension);

marked.setOptions({
  breaks: true,
  gfm: true,
  renderer,
});

const hasIncompleteMarkdown = (
  text: string
): { hasIncomplete: boolean; completeText: string } => {
  // 检查文本末尾是否有不完整的链接或图片语法
  const incompletePatterns = [
    /!\[[^\]]*$/, // 不完整的图片开始：![alt 但没有 ]
    /!\[[^\]]*\]\([^)]*$/, // 不完整的图片URL：![alt](url 但没有 )
    /\[[^\]]*$/, // 不完整的链接开始：[text 但没有 ]
    /\[[^\]]*\]\([^)]*$/, // 不完整的链接URL：[text](url 但没有 )
  ];

  for (const pattern of incompletePatterns) {
    const match = text.match(pattern);
    if (match) {
      // 找到不完整的语法，返回完整部分
      const incompleteStart = match.index!;
      const completeText = text.slice(0, incompleteStart);
      return {
        hasIncomplete: true,
        completeText: completeText + "loading...",
      };
    }
  }

  return { hasIncomplete: false, completeText: text };
};

// Apply syntax highlighting to code blocks (excluding mermaid)
const applySyntaxHighlighting = async () => {
  if (!import.meta.client || !markdownRef.value) return;

  await nextTick();

  // Find all code blocks but exclude mermaid
  const codeBlocks = markdownRef.value.querySelectorAll("pre code");

  for (const codeBlock of codeBlocks) {
    const element = codeBlock as HTMLElement;

    // Skip mermaid code blocks
    if (element.className.includes("language-mermaid")) {
      continue;
    }

    // Apply prism highlighting
    Prism.highlightElement(element);
  }
};

// Initialize image viewer and handle cube chart placeholders
const initViewer = () => {
  if (!import.meta.client || !markdownRef.value) return;

  const images = markdownRef.value.querySelectorAll("img");
  images.forEach((img) => {
    const viewer = new Viewer(img, {
      inline: false,
      button: false,
      navbar: false,
      title: false,
      toolbar: false,
      zoomable: false,
      rotatable: false,
      scalable: false,
      keyboard: false,
      fullscreen: false,
      viewed() {
        viewer.zoomTo(1);
      },
    });
  });
};

const renderMarkdown = async () => {
  if (!import.meta.client || !markdownRef.value) return;

  try {
    // 检测并处理不完整的markdown语法
    const { hasIncomplete, completeText } = hasIncompleteMarkdown(props.source);

    // 如果有不完整的语法，只渲染完整的部分
    const textToRender = hasIncomplete ? completeText : props.source;

    const htmlContent = await marked.parse(textToRender);
    const tempContainer = document.createElement("div");
    tempContainer.innerHTML = htmlContent;

    morphdom(markdownRef.value, tempContainer, {
      childrenOnly: true,
      onBeforeElUpdated: (fromEl, toEl) => {
        // 保护所有图表相关元素
        if (
          fromEl.classList?.contains("cube-chart-container") ||
          fromEl.classList?.contains("cube-chart-loading") ||
          fromEl.closest?.(".cube-chart-container")
        ) {
          return false; // 不更新，保留原有内容
        }

        if (fromEl.className) {
          if (SVG_ELEMENTS.includes(fromEl.tagName.toLowerCase())) {
            return true;
          }
          try {
            toEl.className = fromEl.className;
          } catch {
            // pass
          }
        }
        return true;
      },
      onBeforeNodeDiscarded: (node) => {
        // 防止图表节点被删除
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          if (
            element.classList?.contains("cube-chart-container") ||
            element.classList?.contains("cube-chart-loading")
          ) {
            return false; // 不删除
          }
        }
        return true;
      },
      onNodeAdded: (node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;

          if (!SVG_ELEMENTS.includes(element.tagName.toLowerCase())) {
            element.classList.add("fade-in");
            const cleanupAnimation = () => {
              element.style.willChange = "auto";
              element.classList.remove("fade-in");
            };
            element.addEventListener("animationend", cleanupAnimation, {
              once: true,
            });
            element.addEventListener("webkitAnimationEnd", cleanupAnimation, {
              once: true,
            });
          }
        }
        return node;
      },
    });

    await nextTick();
    await applySyntaxHighlighting();
    initViewer();

    // 处理 cube chart 占位符
    await processCubeChartPlaceholders();
  } catch (error) {
    console.error("Failed to render markdown:", error);
  }
};

// 处理 cube chart 占位符
const processCubeChartPlaceholders = async () => {
  if (!import.meta.client || !markdownRef.value) return;

  const cubeChartPlaceholders = markdownRef.value.querySelectorAll(
    ".cube-chart-placeholder"
  );

  for (const placeholder of cubeChartPlaceholders) {
    const title = placeholder.getAttribute("data-title");
    const cubeQueryStr = placeholder.getAttribute("data-cube-query");

    if (title && cubeQueryStr) {
      try {
        const cubeQuery = JSON.parse(decodeURIComponent(cubeQueryStr));

        // 创建加载占位符
        const loadingContainer = document.createElement("div");
        loadingContainer.className = "my-4 cube-chart-loading";
        loadingContainer.innerHTML = `
          <div class="flex justify-center items-center h-32">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="ml-2">Loading ${title}...</span>
          </div>
        `;

        // 先替换为加载状态
        if (placeholder.parentNode) {
          placeholder.parentNode.replaceChild(loadingContainer, placeholder);
        }

        // 等待一个微任务，确保 DOM 更新
        await nextTick();

        // 动态导入组件
        const [{ createApp }, CubeChart] = await Promise.all([
          import("vue"),
          import("~/modules/monitor/components/CubeChart.vue").then(
            (m) => m.default
          ),
        ]);

        const reactiveQuery = reactive(cubeQuery);
        const chartId = `chart-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        const chartApp = createApp({
          render() {
            return h(CubeChart, {
              cubeQuery: reactiveQuery,
              title,
              chartId,
              tableMaxHeight: "400px",
              chartHeight: "400px",
            });
          },
        });

        // 创建最终的图表容器
        const chartContainer = document.createElement("div");
        chartContainer.className = "my-4 cube-chart-container";

        // 替换加载占位符为实际图表容器
        if (loadingContainer.parentNode) {
          loadingContainer.parentNode.replaceChild(
            chartContainer,
            loadingContainer
          );

          // 等待 DOM 更新后再挂载
          await nextTick();

          chartApp.mount(chartContainer);

          (
            chartContainer as HTMLElement & { __vueApp?: typeof chartApp }
          ).__vueApp = chartApp;
        }
      } catch (error: unknown) {
        console.error("Failed to render cube chart:", error);
      }
    } else {
      console.warn("Missing title or query data for placeholder");
    }
  }
};

onMounted(async () => {
  if (import.meta.client) {
    const { default: mermaid } = await import("mermaid");
    mermaid.initialize({
      // startOnLoad: false,
      theme: "default",
      suppressErrorRendering: true,
      // securityLevel: "loose",
      // fontFamily: "inherit",
    });
    mermaidInstance = mermaid;
  }
  await renderMarkdown();
});

watch(
  () => props.source,
  async () => {
    await renderMarkdown();
  }
);
</script>

<template>
  <div
    ref="markdownRef"
    v-bind="delegatedProps"
    :class="classes"
    :style="props.style"
  />
</template>

<style scoped>
/* Fade-in animation for new elements - Safari optimized */
:deep(.fade-in) {
  animation: fadeIn 0.2s ease-out forwards;
  -webkit-animation: fadeIn 0.2s ease-out forwards;
  /* Safari 优化：启用硬件加速 */
  will-change: opacity, transform;
  /* Safari 优化：防止闪烁 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  /* Safari 优化：强制硬件加速 */
  transform: translate3d(0, 0, 0);
  /* Safari 优化：启用硬件加速合成层 */
  -webkit-transform: translate3d(0, 0, 0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Safari 优化：添加 webkit 前缀的动画 */
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 4px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
  }
}

:deep(.mermaid-diagram) {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

:deep(.mermaid-diagram svg) {
  max-width: 100%;
  height: auto;
}

/* Override prose-stone theme for code blocks with white theme */
:deep(pre) {
  background: #f6f8fa !important;
  border-radius: 10px !important;
  padding: 8px 12px !important;
}

:deep(pre code) {
  background: transparent !important;
  color: #24292f !important;
  line-height: 1.45 !important;
}

/* Prism.js white theme overrides */
:deep(.token.comment),
:deep(.token.prolog),
:deep(.token.doctype),
:deep(.token.cdata) {
  color: #6a737d !important;
}

:deep(.token.punctuation) {
  color: #24292f !important;
}

:deep(.token.property),
:deep(.token.tag),
:deep(.token.boolean),
:deep(.token.number),
:deep(.token.constant),
:deep(.token.symbol),
:deep(.token.deleted) {
  color: #005cc5 !important;
}

:deep(.token.selector),
:deep(.token.attr-name),
:deep(.token.string),
:deep(.token.char),
:deep(.token.builtin),
:deep(.token.inserted) {
  color: #032f62 !important;
}

:deep(.token.operator),
:deep(.token.entity),
:deep(.token.url),
:deep(.language-css .token.string),
:deep(.style .token.string) {
  color: #d73a49 !important;
}

:deep(.token.atrule),
:deep(.token.attr-value),
:deep(.token.keyword) {
  color: #d73a49 !important;
}

:deep(.token.function),
:deep(.token.class-name) {
  color: #6f42c1 !important;
}

:deep(.token.regex),
:deep(.token.important),
:deep(.token.variable) {
  color: #e36209 !important;
}

:deep(.token.important),
:deep(.token.bold) {
  font-weight: bold !important;
}

:deep(.token.italic) {
  font-style: italic !important;
}
</style>
