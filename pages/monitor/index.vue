<script setup lang="ts">
import { useRouter } from "vue-router";
import AddDashboardCard from "@/modules/monitor/components/AddDashboardCard.vue";
import DashboardCard from "@/modules/monitor/components/DashboardCard.vue";
import DashboardCardActions from "@/modules/monitor/components/DashboardCardActions.vue";

const router = useRouter();

definePageMeta({
  layout: "monitor",
});

const { data: dashboards, refresh: refreshDashboards } = await useAsyncData(
  "dashboards",
  () => {
    const headers = useRequestHeaders(["cookie"]);
    return $fetch("/api/dashboard", {
      headers,
    });
  }
);

const createDashboard = async () => {
  const dashboard = await $fetch("/api/dashboard", {
    method: "POST",
  });
  router.push(`/monitor/board/edit/${dashboard.id}`);
};

const handleCardClick = (dashboardId: number) => {
  router.push(`/monitor/board/${dashboardId}`);
};

const handleEditClick = (dashboardId: number) => {
  router.push(`/monitor/board/edit/${dashboardId}`);
};

const handleDeleteClick = (dashboardId: number) => {
  deleteDashboard(dashboardId);
};

const deleteDashboard = async (id: number) => {
  await $fetch(`/api/dashboard/${id}`, {
    method: "DELETE",
  });
  await refreshDashboards();
};
</script>

<template>
  <ClientOnly>
    <Teleport to="#page-header-left">
      <span>Monitor</span>
    </Teleport>
  </ClientOnly>

  <div class="p-6" style="--color-theme-color: #3a7ca5">
    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      <!-- 添加新仪表板卡片 -->
      <AddDashboardCard :create-dashboard="createDashboard" />

      <!-- 现有仪表板卡片 -->
      <DashboardCard
        v-for="dashboard in dashboards"
        :key="dashboard.id"
        :dashboard="dashboard"
        :on-card-click="() => handleCardClick(dashboard.id)"
      >
        <template #actions>
          <DashboardCardActions
            :on-edit="() => handleEditClick(dashboard.id)"
            :on-delete="() => handleDeleteClick(dashboard.id)"
          />
        </template>
      </DashboardCard>
    </div>
  </div>
</template>
