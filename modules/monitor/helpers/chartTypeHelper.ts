import type { Annotation, Query } from "@cubejs-client/core";
import { ResultSet } from "@cubejs-client/core";
import type { ChartDataType, ChartResult, TableDataItem } from "../types";

// 公共字段提取工具函数
function extract(
  metaData: Record<string, Record<string, Annotation>>,
  field: string,
  result: Record<string, string>
) {
  if (metaData[field]) {
    Object.entries(metaData[field]).forEach(([key, annotation]) => {
      result[key] = annotation.shortTitle || key;
    });
  }
}

export const extractMetaDataTitles = (
  metaData: Record<string, Record<string, Annotation>>
): Record<string, string> => {
  const result: Record<string, string> = {};
  extract(metaData, "dimensions", result);
  extract(metaData, "measures", result);
  extract(metaData, "timeDimensions", result);
  extract(metaData, "segments", result);
  return result;
};

const getChartData = (
  chartData: Record<string, string | number>[],
  allTitles: Record<string, string>,
  _lineMeasures?: string[]
): ChartDataType => {
  if (!chartData || !Array.isArray(chartData)) {
    return [];
  }
  // 提取所有的系列名称（排除 x, xValues 等系统字段）
  const systemFields = ["x", "xValues"];
  const firstItem = chartData[0] || {};
  const seriesNames = Object.keys(firstItem).filter(
    (key) => !systemFields.includes(key)
  );

  // 为每个系列创建数据点数组
  const result: ChartDataType = [];

  seriesNames.forEach((seriesName) => {
    const seriesData = chartData
      .map((item) => ({
        label: item.x,
        value: item[seriesName],
        category: allTitles[seriesName] ? allTitles[seriesName] : seriesName,
      }))
      .filter((point) => point.value !== undefined && point.value !== null);

    result.push(...seriesData);
  });

  return result;
};

export function getChartTypeAndData(
  response: ResultSet | { query?: Query; data?: object[] }
): ChartResult {
  if (response instanceof ResultSet) {
    const query = response.query();
    const timeDimensions = query?.timeDimensions || [];
    const measures = query?.measures || null;
    const dimensions = query?.dimensions || null;
    const metaData = response.annotation();
    //单位判断
    const measureUnits = Object.values(metaData.measures || {}).map(
      (m) => m?.meta?.unit
    );

    const allUnitsSame = measureUnits.every((u) => u === measureUnits[0]);
    const allTitles = extractMetaDataTitles(metaData);
    //图表显示
    if (
      timeDimensions.length === 1 &&
      allUnitsSame &&
      timeDimensions[0].granularity
    ) {
      const chartData = response.chartPivot();
      const transformedData = getChartData(
        chartData,
        allTitles,
        Object.keys(metaData.measures)
      );
      if ((dimensions?.length ?? 0) >= 1 && (measures?.length ?? 0) === 1) {
        return {
          type: "line",
          data: transformedData,
          unit: measureUnits[0],
        };
      }
      if (dimensions?.length === 0 && (measures?.length ?? 0) >= 1) {
        return {
          type: "line",
          data: transformedData,
          unit: measureUnits[0],
        };
      }
    }

    if (
      !timeDimensions?.length &&
      measures &&
      dimensions &&
      measures?.length > 1 &&
      dimensions?.length > 1
    ) {
      const chartData = response.chartPivot();
      const transformedData = getChartData(chartData, allTitles);

      return {
        type: "bar",
        data: transformedData,
        unit: measureUnits[0],
      };
    }

    const rawTable = response.tablePivot() as TableDataItem[];
    const tableWithTitles = rawTable.map((row) => {
      const newRow: Record<string, string | number | string[] | undefined> = {};
      Object.entries(row).forEach(([key, value]) => {
        const title = allTitles[key];
        newRow[title] = value;
      });
      return newRow;
    });

    const measuresData: Record<string, string> = {};
    extract(metaData, "measures", measuresData);

    // 获取table单位
    const measureUnitsMap: Record<string, string> = {};
    Object.entries(metaData.measures || {}).forEach(([key, annotation]) => {
      const title = annotation.shortTitle || key;
      const unit = annotation?.meta?.unit;
      if (unit) {
        measureUnitsMap[title] = unit;
      }
    });
    return {
      type: "table",
      data: tableWithTitles,
      measuresData: Object.values(measuresData),
      measureUnits: measureUnitsMap,
    };
  } else {
    return {
      type: "table",
      data: response.data as TableDataItem[],
    };
  }
}
