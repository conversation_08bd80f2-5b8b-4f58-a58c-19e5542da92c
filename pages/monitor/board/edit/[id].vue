<script setup lang="ts">
import { useMonitorEditStore } from "~/modules/monitor/stores/monitorEditStore";
import MonitorChat from "~/modules/monitor/components/MonitorChat.vue";
import DashboardCanvas from "~/modules/monitor/components/DashboardCanvas.vue";

const monitorEditStore = useMonitorEditStore();
const { dashboard } = storeToRefs(monitorEditStore);

useHead({
  title: `${dashboard?.value?.name ?? ""} - Monitor`,
});

definePageMeta({
  layout: "monitor",
});
</script>

<template>
  <ClientOnly>
    <Teleport to="#page-header-left">
      <div class="px-5">Monitor - {{ dashboard?.name }}</div>
    </Teleport>
  </ClientOnly>

  <div class="flex h-[calc(100vh-64px)]">
    <MonitorChat />
    <DashboardCanvas />
  </div>
</template>
