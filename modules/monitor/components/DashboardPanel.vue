<script setup lang="ts">
import type { Chart } from "../types";
import CubeChart from "./CubeChart.vue";

const props = defineProps<{
  config: Chart;
  debug?: boolean;
}>();
</script>

<template>
  <div class="w-full h-full border rounded-lg p-3 shadow">
    <CubeChart
      v-if="!debug"
      :chart-id="props.config.id"
      :cube-query="props.config.query"
      :title="props.config.title"
    />
    <div v-else class="w-full h-full overflow-y-auto p-3">
      <pre class="text-xs">{{ JSON.stringify(props.config, null, 2) }}</pre>
    </div>
  </div>
</template>
