<script setup lang="ts">
import { Button } from "@/components/ui/button";
import { ChevronDownIcon } from "lucide-vue-next";
import { ref, onMounted, onUnmounted } from "vue";

const { isRunning } = defineProps<{
  isRunning?: boolean;
}>();

const isVisible = ref(false);

const emit = defineEmits<{
  (e: "to-bottom"): void;
}>();

const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: "smooth",
  });
  emit("to-bottom");
};

const handleVisible = () => {
  const isNearBottom =
    window.innerHeight + window.scrollY >= document.body.offsetHeight - 100;
  isVisible.value = !isNearBottom;
};

let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  window.addEventListener("scroll", handleVisible);
  handleVisible();
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        handleVisible();
      });
    });
    resizeObserver.observe(document.body);
  }
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleVisible);
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<template>
  <Button
    variant="outline"
    size="icon"
    class="rounded-full absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
      'border-[#e2ecf2] border-t-theme-color animate-spin':
        isRunning && isVisible,
      'shadow-md': !isRunning,
    }"
    @click="scrollToBottom"
  >
    <div
      :class="{ 'animate-reverse-spin': isRunning && isVisible }"
      class="flex items-center justify-center w-full h-full"
      style="transform-origin: center; backface-visibility: hidden"
    >
      <ChevronDownIcon :size="16" />
    </div>
  </Button>
</template>

<style scoped>
@keyframes reverse-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

.animate-reverse-spin {
  animation: reverse-spin 1s linear infinite;
  will-change: transform;
}
</style>
