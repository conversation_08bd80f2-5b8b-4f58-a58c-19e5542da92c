<script setup lang="ts">
import InteractiveTool from "@/modules/sentire/components/InteractiveTools/InteractiveTool.vue";
import { LightbulbIcon } from "lucide-vue-next";
import PendingOverlay from "~/components/PendingOverlay.vue";

const props = defineProps<{
  question: string;
  type: string;
  options: string[];
}>();

const emit = defineEmits<{
  (e: "submit", value: string[]): void;
}>();

const pending = ref(false);

const handleSubmit = (value: string[]) => {
  pending.value = true;
  emit("submit", value);
};
</script>

<template>
  <PendingOverlay :is-pending="pending">
    <div
      class="p-4 border border-stone-200 bg-stone-50 rounded-xl overflow-hidden"
    >
      <div class="flex items-start mb-3">
        <div class="size-7 min-w-7 flex items-center justify-center mr-1">
          <LightbulbIcon :size="20" class="text-stone-500 inline-block" />
        </div>
        <p>
          {{ props.question }}
        </p>
      </div>
      <div class="pl-8">
        <InteractiveTool
          :type="props.type as string"
          :options="props.options as string[]"
          @submit="handleSubmit"
        />
      </div>
    </div>
  </PendingOverlay>
</template>
