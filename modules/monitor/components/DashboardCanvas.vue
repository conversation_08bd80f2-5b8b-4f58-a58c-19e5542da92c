<script setup lang="ts">
import { useMonitorEditStore } from "../stores/monitorEditStore";
import { GridLayout } from "grid-layout-plus";
import DashboardPanel from "./DashboardPanel.vue";
import PlaceholderPanel from "./PlaceholderPanel.vue";
import { BugIcon } from "lucide-vue-next";
import { DateTimeRangePicker } from "@/components/ui/datetime-range-picker";
import type { Chart } from "../types";
import dayjs from "dayjs";

const monitorEditStore = useMonitorEditStore();
const { dashboardLayout, dashboardCharts } = storeToRefs(monitorEditStore);
const enableDebug = ref(false);
const timeRange = ref<{ start: Date; end: Date }>({
  start: dayjs().subtract(7, "day").startOf("day").toDate(),
  end: dayjs().toDate(),
});
const config = useRuntimeConfig();
const debugCubeQuery = computed(
  () => config.public.debugCubeQuery.toLowerCase() === "true"
);

const charts = computed(() => {
  const newCharts: Record<string, Chart> = {};
  for (const [id, config] of Object.entries(dashboardCharts.value)) {
    if (config.query.timeDimensions) {
      newCharts[id] = {
        ...config,
        query: {
          ...config.query,
          timeDimensions: [
            {
              ...config.query.timeDimensions[0],
              dateRange: [
                timeRange.value.start.toISOString(),
                timeRange.value.end.toISOString(),
              ],
            },
          ],
        },
      };
    } else {
      const tableName = (
        config.query.dimensions?.[0] ?? config.query.measures?.[0]
      )?.split(".")[0];
      if (tableName) {
        newCharts[id] = {
          ...config,
          query: {
            ...config.query,
            timeDimensions: [
              {
                dimension: `${tableName}.timestamp`,
                dateRange: [
                  timeRange.value.start.toISOString(),
                  timeRange.value.end.toISOString(),
                ],
              },
            ],
          },
        };
      } else {
        newCharts[id] = { ...config };
      }
    }
  }
  return newCharts;
});
</script>

<template>
  <div class="p-3 w-full h-full overflow-y-auto">
    <ClientOnly>
      <Teleport to="#page-header-right">
        <div class="flex justify-end gap-2">
          <Button
            v-if="debugCubeQuery"
            class="rounded-full"
            variant="ghost"
            @click="enableDebug = !enableDebug"
          >
            <BugIcon :size="18" />
          </Button>

          <DateTimeRangePicker v-model:value="timeRange" />
        </div>
      </Teleport>
    </ClientOnly>

    <GridLayout
      v-model:layout="dashboardLayout"
      :col-num="12"
      :row-height="80"
      :is-draggable="false"
      :is-resizable="false"
      vertical-compact
      use-css-transforms
    >
      <template #item="{ item }">
        <DashboardPanel
          v-if="charts[item.i]"
          :config="charts[item.i]"
          :debug="enableDebug"
        />
        <PlaceholderPanel v-else />
      </template>
    </GridLayout>
  </div>
</template>
