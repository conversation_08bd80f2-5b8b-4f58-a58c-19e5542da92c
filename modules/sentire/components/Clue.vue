<script setup lang="ts">
import type { Clue, ClueWithData } from "~/types/sentire/clue";
import { useCurrentThreadStore } from "../stores/currentThreadStore";
import MiniChart from "./MiniChart.vue";
import dayjs from "dayjs";
import { computed } from "vue";
import type { SparklineDataPoint } from "@/types/sentire/clue";
import { DEFAULT_CUSTOM_API_ENDPOINT } from "../const";

const currentThreadStore = useCurrentThreadStore();

const props = defineProps<{
  linkId: string;
  packId: string;
  clue: Clue;
}>();

const { data: clue, pending } = useAsyncData(
  `thread-${props.clue.thread_id}-link-${props.linkId}-pack-${props.clue.pack_id}-clue-${props.clue.clue_id}`,
  async () => {
    const res = await $fetch<ClueWithData>(
      `${DEFAULT_CUSTOM_API_ENDPOINT}/clue/${props.clue.id}`
    );
    currentThreadStore.updateNetworkLinkCheckStatus(res);
    return res;
  }
);

type CheckpointStatus = "unknown" | "error" | "success" | "warning";

const getCheckpointClass = (
  type: "status" | "text",
  status: CheckpointStatus
) => {
  const map = {
    status: {
      unknown: "bg-title-bg text-checking-text",
      success: "bg-success-bg text-success-text",
      warning: "bg-warning-bg text-warning-text",
      error: "bg-error-bg text-error-text",
    },
    text: {
      unknown: "text-checking-text",
      warning: "text-warning-text",
      error: "text-error-text",
    },
  };
  return (map[type] as Record<CheckpointStatus, string>)[status] || "";
};

const getCheckpointStatusText = (status: string) => {
  switch (status) {
    case "checking":
      return "Pending";
    case "success":
      return "CLEAR";
    case "warning":
      return "ATTENTION";
    case "error":
      return "SUSPICIOUS";
    default:
      return "CLEAR";
  }
};

const clueStatus = computed<CheckpointStatus>(
  () => (clue.value?.health?.status ?? "unknown") as CheckpointStatus
);
</script>

<template>
  <div class="px-2 bg-white text-default-text">
    <div class="flex items-center text-sm">
      <span
        class="w-[120px] inline-block pt-[2px] pb-[2px] pr-4 pl-4 rounded text-center flex-shrink-0"
        :class="
          pending
            ? 'bg-title-bg text-checking-text pending-blink'
            : getCheckpointClass('status', clueStatus)
        "
      >
        {{ pending ? "Pending" : getCheckpointStatusText(clueStatus) }}
      </span>
      <div
        class="flex-1 px-4"
        :class="
          pending
            ? 'text-checking-text pending-blink'
            : getCheckpointClass('text', clueStatus)
        "
      >
        {{ pending ? "Pending Analysis..." : clue?.health?.message }}
      </div>
      <div
        class="w-[180px] h-12 rounded flex items-center justify-center flex-shrink-0 ml-4 bg-white"
      >
        <template v-if="pending">
          <div class="w-full bg-title-bg rounded animate-pulse h-10 my-auto" />
        </template>
        <template v-else>
          <MiniChart
            height="100%"
            :chart-range="
              clue?.health?.status === 'success'
                ? undefined
                : {
                    minRange: clue?.health?.clue_start_time
                      ? dayjs(clue?.health?.clue_start_time).valueOf()
                      : undefined,
                    maxRange: clue?.health?.clue_end_time
                      ? dayjs(clue?.health?.clue_end_time).valueOf()
                      : undefined,
                  }
            "
            :unit="clue?.input_json.checkpoint.sparkline.metric?.unit"
            :data="
              clue?.sparkline?.map((item: SparklineDataPoint) => ({
                ...item,
                time: dayjs(item.time).valueOf(),
              }))
            "
            :chart-status="clue?.health?.status as string"
          />
        </template>
      </div>
    </div>
  </div>
</template>
<style scoped>
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}
.pending-blink {
  animation: blink 1.2s infinite;
}
</style>
